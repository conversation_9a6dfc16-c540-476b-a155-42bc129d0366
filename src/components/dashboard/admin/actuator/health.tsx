'use client';

import { useState } from 'react';
import { 
  Heart, 
  Database, 
  HardDrive, 
  Mail, 
  Shield, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Info,
  RefreshCw,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface ActuatorHealthProps {
  readonly healthStatus: any;
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
}

export function ActuatorHealth({ 
  healthStatus, 
  isLoading, 
  error, 
  onRefresh 
}: ActuatorHealthProps) {
  const [expandedComponents, setExpandedComponents] = useState<Set<string>>(new Set());

  const toggleComponent = (componentName: string) => {
    const newExpanded = new Set(expandedComponents);
    if (newExpanded.has(componentName)) {
      newExpanded.delete(componentName);
    } else {
      newExpanded.add(componentName);
    }
    setExpandedComponents(newExpanded);
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'UP':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'DOWN':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'OUT_OF_SERVICE':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'UNKNOWN':
        return <Info className="h-5 w-5 text-gray-600" />;
      default:
        return <Info className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'UP':
        return 'text-green-600 bg-green-100 border-green-200';
      case 'DOWN':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'OUT_OF_SERVICE':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'UNKNOWN':
        return 'text-gray-600 bg-gray-100 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'UP':
        return 'Çalışıyor';
      case 'DOWN':
        return 'Çalışmıyor';
      case 'OUT_OF_SERVICE':
        return 'Hizmet Dışı';
      case 'UNKNOWN':
        return 'Bilinmiyor';
      default:
        return status || 'Bilinmiyor';
    }
  };

  const getComponentIcon = (componentName: string) => {
    switch (componentName.toLowerCase()) {
      case 'db':
        return <Database className="h-4 w-4" />;
      case 'diskspace':
        return <HardDrive className="h-4 w-4" />;
      case 'mail':
        return <Mail className="h-4 w-4" />;
      case 'ssl':
        return <Shield className="h-4 w-4" />;
      case 'ping':
        return <Heart className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getComponentDisplayName = (componentName: string) => {
    switch (componentName.toLowerCase()) {
      case 'db':
        return 'Veritabanı';
      case 'diskspace':
        return 'Disk Alanı';
      case 'mail':
        return 'Mail Servisi';
      case 'ssl':
        return 'SSL Sertifikası';
      case 'ping':
        return 'Ping Testi';
      default:
        return componentName;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-32" />
          </CardContent>
        </Card>
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-5 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-6 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-red-600">
            <XCircle className="h-5 w-5" />
            <span className="font-medium">Hata: {error}</span>
          </div>
          <Button onClick={onRefresh} className="mt-4" variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Tekrar Dene
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overall Health Status */}
      <Card className={healthStatus ? getStatusColor(healthStatus.status) : ''}>
        <CardHeader>
          <div className="flex items-center gap-3">
            {healthStatus && getStatusIcon(healthStatus.status)}
            <div>
              <CardTitle className="text-xl">Genel Sistem Durumu</CardTitle>
              <CardDescription>
                Tüm sistem bileşenlerinin sağlık durumu
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <Badge 
              variant="outline" 
              className={`text-lg px-4 py-2 ${healthStatus ? getStatusColor(healthStatus.status) : ''}`}
            >
              {healthStatus ? getStatusText(healthStatus.status) : 'Yükleniyor...'}
            </Badge>
            <Button onClick={onRefresh} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Yenile
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Component Health Details */}
      {healthStatus?.components && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Bileşen Detayları</h3>
          
          {Object.entries(healthStatus.components).map(([componentName, component]: [string, any]) => (
            <Card key={componentName} className={getStatusColor(component.status)}>
              <Collapsible 
                open={expandedComponents.has(componentName)}
                onOpenChange={() => toggleComponent(componentName)}
              >
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getComponentIcon(componentName)}
                        <div>
                          <CardTitle className="text-base">
                            {getComponentDisplayName(componentName)}
                          </CardTitle>
                          <CardDescription>
                            {component.status && getStatusText(component.status)}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(component.status)}
                        {expandedComponents.has(componentName) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>
                
                <CollapsibleContent>
                  <CardContent className="pt-0">
                    {component.details && (
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Detaylar:</h4>
                        <div className="bg-muted/50 rounded-lg p-3 space-y-1">
                          {Object.entries(component.details).map(([key, value]: [string, any]) => (
                            <div key={key} className="flex justify-between text-sm">
                              <span className="font-medium capitalize">
                                {key === 'database' ? 'Veritabanı' :
                                 key === 'validationQuery' ? 'Doğrulama Sorgusu' :
                                 key === 'total' ? 'Toplam' :
                                 key === 'free' ? 'Boş' :
                                 key === 'threshold' ? 'Eşik' :
                                 key === 'path' ? 'Yol' :
                                 key === 'exists' ? 'Mevcut' :
                                 key === 'location' ? 'Konum' :
                                 key === 'validChains' ? 'Geçerli Zincirler' :
                                 key === 'invalidChains' ? 'Geçersiz Zincirler' :
                                 key}:
                              </span>
                              <span className="text-muted-foreground">
                                {typeof value === 'number' && (key === 'total' || key === 'free') ? 
                                  formatBytes(value) : 
                                  typeof value === 'boolean' ? 
                                    (value ? 'Evet' : 'Hayır') :
                                    Array.isArray(value) ?
                                      value.length === 0 ? 'Boş' : value.join(', ') :
                                      String(value)
                                }
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
