'use client';

import { useState, useEffect } from 'react';
import { 
  BarChart3, 
  Cpu, 
  Database, 
  Server, 
  Clock, 
  Activity,
  RefreshCw,
  Search,
  TrendingUp,
  HardDrive,
  Zap
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { actuatorService } from '@/lib/api';

export function ActuatorMetrics() {
  const [metrics, setMetrics] = useState<any>(null);
  const [selectedMetrics, setSelectedMetrics] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('jvm');

  const loadMetrics = async () => {
    setLoading(true);
    try {
      const metricsData = await actuatorService.getMetrics();
      setMetrics(metricsData);
      
      // Load key metrics for each category
      await loadCategoryMetrics();
    } catch (error) {
      console.error('Metrics load error:', error);
      toast.error('Metrikler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const loadCategoryMetrics = async () => {
    try {
      const keyMetrics = [
        'jvm.memory.used',
        'jvm.memory.max',
        'jvm.threads.live',
        'jvm.threads.peak',
        'jvm.gc.pause',
        'system.cpu.usage',
        'process.cpu.usage',
        'system.cpu.count',
        'hikaricp.connections.active',
        'hikaricp.connections.max',
        'hikaricp.connections.idle',
        'http.server.requests',
        'http.server.requests.active',
        'process.uptime',
        'application.started.time',
        'disk.free',
        'disk.total'
      ];

      const metricPromises = keyMetrics.map(async (metricName) => {
        try {
          const metric = await actuatorService.getMetric(metricName);
          return { [metricName]: metric };
        } catch (error) {
          return { [metricName]: null };
        }
      });

      const results = await Promise.all(metricPromises);
      const metricsMap = results.reduce((acc, curr) => ({ ...acc, ...curr }), {});
      setSelectedMetrics(metricsMap);
    } catch (error) {
      console.error('Category metrics load error:', error);
    }
  };

  useEffect(() => {
    loadMetrics();
  }, []);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}g ${hours}s ${minutes}d`;
    if (hours > 0) return `${hours}s ${minutes}d`;
    return `${minutes}d`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getMetricValue = (metric: any, index: number = 0) => {
    return metric?.measurements?.[index]?.value || 0;
  };

  const filteredMetrics = metrics?.names?.filter((name: string) =>
    name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const categorizeMetrics = (metricNames: string[]) => {
    const categories = {
      jvm: metricNames.filter(name => name.startsWith('jvm.')),
      http: metricNames.filter(name => name.startsWith('http.')),
      database: metricNames.filter(name => name.startsWith('hikaricp.') || name.startsWith('jdbc.')),
      system: metricNames.filter(name => name.startsWith('system.') || name.startsWith('process.') || name.startsWith('disk.')),
      security: metricNames.filter(name => name.startsWith('spring.security.')),
      application: metricNames.filter(name => name.startsWith('application.') || name.startsWith('tasks.') || name.startsWith('tomcat.')),
      other: metricNames.filter(name => 
        !name.startsWith('jvm.') && 
        !name.startsWith('http.') && 
        !name.startsWith('hikaricp.') && 
        !name.startsWith('jdbc.') &&
        !name.startsWith('system.') && 
        !name.startsWith('process.') && 
        !name.startsWith('disk.') &&
        !name.startsWith('spring.security.') &&
        !name.startsWith('application.') &&
        !name.startsWith('tasks.') &&
        !name.startsWith('tomcat.')
      )
    };
    return categories;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-3">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-6 w-16" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const categories = categorizeMetrics(filteredMetrics);

  return (
    <div className="space-y-6">
      {/* Header with Search */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h3 className="text-lg font-semibold">Sistem Metrikleri</h3>
          <p className="text-sm text-muted-foreground">
            Toplam {metrics?.names?.length || 0} metrik mevcut
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Metrik ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <Button onClick={loadMetrics} disabled={loading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* JVM Memory */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Server className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">JVM Bellek</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {selectedMetrics['jvm.memory.used'] && selectedMetrics['jvm.memory.max'] ? (
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {formatBytes(getMetricValue(selectedMetrics['jvm.memory.used']))}
                </div>
                <p className="text-xs text-muted-foreground">
                  / {formatBytes(getMetricValue(selectedMetrics['jvm.memory.max']))}
                </p>
              </div>
            ) : (
              <Skeleton className="h-8 w-20" />
            )}
          </CardContent>
        </Card>

        {/* CPU Usage */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Cpu className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">CPU Kullanımı</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {selectedMetrics['system.cpu.usage'] ? (
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {formatPercentage(getMetricValue(selectedMetrics['system.cpu.usage']))}
                </div>
                <p className="text-xs text-muted-foreground">
                  {selectedMetrics['system.cpu.count'] ? 
                    `${Math.round(getMetricValue(selectedMetrics['system.cpu.count']))} çekirdek` : 
                    'Sistem'
                  }
                </p>
              </div>
            ) : (
              <Skeleton className="h-8 w-20" />
            )}
          </CardContent>
        </Card>

        {/* Active Threads */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">Thread'ler</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {selectedMetrics['jvm.threads.live'] ? (
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {Math.round(getMetricValue(selectedMetrics['jvm.threads.live']))}
                </div>
                <p className="text-xs text-muted-foreground">
                  {selectedMetrics['jvm.threads.peak'] ? 
                    `Pik: ${Math.round(getMetricValue(selectedMetrics['jvm.threads.peak']))}` : 
                    'Aktif'
                  }
                </p>
              </div>
            ) : (
              <Skeleton className="h-8 w-20" />
            )}
          </CardContent>
        </Card>

        {/* Database Connections */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">DB Bağlantıları</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {selectedMetrics['hikaricp.connections.active'] ? (
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {Math.round(getMetricValue(selectedMetrics['hikaricp.connections.active']))}
                </div>
                <p className="text-xs text-muted-foreground">
                  {selectedMetrics['hikaricp.connections.max'] ? 
                    `/ ${Math.round(getMetricValue(selectedMetrics['hikaricp.connections.max']))} maks` : 
                    'Aktif'
                  }
                </p>
              </div>
            ) : (
              <Skeleton className="h-8 w-20" />
            )}
          </CardContent>
        </Card>
      </div>

      {/* Categorized Metrics */}
      <Tabs value={activeCategory} onValueChange={setActiveCategory} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 lg:grid-cols-7">
          <TabsTrigger value="jvm">JVM</TabsTrigger>
          <TabsTrigger value="http">HTTP</TabsTrigger>
          <TabsTrigger value="database">Veritabanı</TabsTrigger>
          <TabsTrigger value="system">Sistem</TabsTrigger>
          <TabsTrigger value="security">Güvenlik</TabsTrigger>
          <TabsTrigger value="application">Uygulama</TabsTrigger>
          <TabsTrigger value="other">Diğer</TabsTrigger>
        </TabsList>

        {Object.entries(categories).map(([category, metricNames]) => (
          <TabsContent key={category} value={category} className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-md font-medium">
                {category === 'jvm' ? 'JVM Metrikleri' :
                 category === 'http' ? 'HTTP Metrikleri' :
                 category === 'database' ? 'Veritabanı Metrikleri' :
                 category === 'system' ? 'Sistem Metrikleri' :
                 category === 'security' ? 'Güvenlik Metrikleri' :
                 category === 'application' ? 'Uygulama Metrikleri' :
                 'Diğer Metrikler'}
              </h4>
              <Badge variant="secondary">
                {metricNames.length} metrik
              </Badge>
            </div>
            
            <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
              {metricNames.map((metricName: string) => (
                <Card key={metricName} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate" title={metricName}>
                          {metricName}
                        </p>
                        {selectedMetrics[metricName] && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {selectedMetrics[metricName].description}
                          </p>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={async () => {
                          try {
                            const metric = await actuatorService.getMetric(metricName);
                            setSelectedMetrics((prev: any) => ({ ...prev, [metricName]: metric }));
                            toast.success(`${metricName} metriği güncellendi`);
                          } catch (error) {
                            toast.error('Metrik yüklenirken hata oluştu');
                          }
                        }}
                      >
                        <Activity className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
