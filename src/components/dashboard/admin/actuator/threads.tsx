'use client';

import { useState, useEffect } from 'react';
import { 
  Clock, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Pause,
  RefreshCw,
  Search,
  Filter,
  Download,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';
import { actuatorService } from '@/lib/api';

export function ActuatorThreads() {
  const [threadDump, setThreadDump] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [stateFilter, setStateFilter] = useState('all');
  const [expandedThreads, setExpandedThreads] = useState<Set<string>>(new Set());

  const loadThreadDump = async () => {
    setLoading(true);
    try {
      const dumpData = await actuatorService.getThreadDump();
      setThreadDump(dumpData);
    } catch (error) {
      console.error('Thread dump load error:', error);
      toast.error('Thread dump alınırken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadThreadDump();
  }, []);

  const toggleThread = (threadId: string) => {
    const newExpanded = new Set(expandedThreads);
    if (newExpanded.has(threadId)) {
      newExpanded.delete(threadId);
    } else {
      newExpanded.add(threadId);
    }
    setExpandedThreads(newExpanded);
  };

  const getStateIcon = (state: string) => {
    switch (state?.toUpperCase()) {
      case 'RUNNABLE':
        return <Activity className="h-4 w-4 text-green-600" />;
      case 'BLOCKED':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'WAITING':
      case 'TIMED_WAITING':
        return <Pause className="h-4 w-4 text-yellow-600" />;
      case 'TERMINATED':
        return <CheckCircle className="h-4 w-4 text-gray-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStateColor = (state: string) => {
    switch (state?.toUpperCase()) {
      case 'RUNNABLE':
        return 'text-green-600 bg-green-100 border-green-200';
      case 'BLOCKED':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'WAITING':
      case 'TIMED_WAITING':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'TERMINATED':
        return 'text-gray-600 bg-gray-100 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getStateDisplayName = (state: string) => {
    switch (state?.toUpperCase()) {
      case 'RUNNABLE':
        return 'Çalışıyor';
      case 'BLOCKED':
        return 'Bloklu';
      case 'WAITING':
        return 'Bekliyor';
      case 'TIMED_WAITING':
        return 'Zamanlı Bekliyor';
      case 'TERMINATED':
        return 'Sonlandırıldı';
      case 'NEW':
        return 'Yeni';
      default:
        return state || 'Bilinmiyor';
    }
  };

  const downloadThreadDump = () => {
    if (!threadDump) return;
    
    const content = JSON.stringify(threadDump, null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `thread-dump-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Thread dump indirildi');
  };

  const filteredThreads = threadDump?.threads?.filter((thread: any) => {
    const matchesSearch = !searchTerm || 
      thread.threadName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      thread.threadState?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesState = stateFilter === 'all' || 
      thread.threadState?.toUpperCase() === stateFilter.toUpperCase();
    
    return matchesSearch && matchesState;
  }) || [];

  const getThreadStatistics = () => {
    if (!threadDump?.threads) return {};
    
    const stats = threadDump.threads.reduce((acc: any, thread: any) => {
      const state = thread.threadState?.toUpperCase() || 'UNKNOWN';
      acc[state] = (acc[state] || 0) + 1;
      return acc;
    }, {});
    
    return stats;
  };

  const statistics = getThreadStatistics();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-3">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-6 w-16" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h3 className="text-lg font-semibold">Thread Durumu</h3>
          <p className="text-sm text-muted-foreground">
            Toplam {threadDump?.threads?.length || 0} thread
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Thread ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-48"
            />
          </div>
          <Select value={stateFilter} onValueChange={setStateFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Durum filtrele" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tümü</SelectItem>
              <SelectItem value="runnable">Çalışıyor</SelectItem>
              <SelectItem value="blocked">Bloklu</SelectItem>
              <SelectItem value="waiting">Bekliyor</SelectItem>
              <SelectItem value="timed_waiting">Zamanlı Bekliyor</SelectItem>
              <SelectItem value="terminated">Sonlandırıldı</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={downloadThreadDump} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            İndir
          </Button>
          <Button onClick={loadThreadDump} disabled={loading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
        </div>
      </div>

      {/* Thread Statistics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-green-600" />
              <CardTitle className="text-sm font-medium">Çalışıyor</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {statistics.RUNNABLE || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <CardTitle className="text-sm font-medium">Bloklu</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {statistics.BLOCKED || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Pause className="h-4 w-4 text-yellow-600" />
              <CardTitle className="text-sm font-medium">Bekliyor</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {(statistics.WAITING || 0) + (statistics.TIMED_WAITING || 0)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-gray-600" />
              <CardTitle className="text-sm font-medium">Sonlandırıldı</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">
              {statistics.TERMINATED || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">Toplam</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {threadDump?.threads?.length || 0}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Thread List */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-md font-medium">Thread Listesi</h4>
          <Badge variant="secondary">
            {filteredThreads.length} / {threadDump?.threads?.length || 0} thread
          </Badge>
        </div>

        <div className="space-y-2">
          {filteredThreads.map((thread: any, index: number) => (
            <Card key={thread.threadId || index} className={getStateColor(thread.threadState)}>
              <Collapsible 
                open={expandedThreads.has(thread.threadId?.toString() || index.toString())}
                onOpenChange={() => toggleThread(thread.threadId?.toString() || index.toString())}
              >
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors py-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getStateIcon(thread.threadState)}
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-sm font-medium truncate">
                            {thread.threadName || `Thread ${thread.threadId}`}
                          </CardTitle>
                          <CardDescription className="text-xs">
                            ID: {thread.threadId} | Durum: {getStateDisplayName(thread.threadState)}
                            {thread.priority && ` | Öncelik: ${thread.priority}`}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {getStateDisplayName(thread.threadState)}
                        </Badge>
                        {expandedThreads.has(thread.threadId?.toString() || index.toString()) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>
                
                <CollapsibleContent>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      {/* Thread Details */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium">ID:</span>
                          <div className="text-muted-foreground">{thread.threadId}</div>
                        </div>
                        <div>
                          <span className="font-medium">Durum:</span>
                          <div className="text-muted-foreground">{getStateDisplayName(thread.threadState)}</div>
                        </div>
                        {thread.priority && (
                          <div>
                            <span className="font-medium">Öncelik:</span>
                            <div className="text-muted-foreground">{thread.priority}</div>
                          </div>
                        )}
                        {thread.daemon !== undefined && (
                          <div>
                            <span className="font-medium">Daemon:</span>
                            <div className="text-muted-foreground">{thread.daemon ? 'Evet' : 'Hayır'}</div>
                          </div>
                        )}
                      </div>

                      {/* Stack Trace */}
                      {thread.stackTrace && thread.stackTrace.length > 0 && (
                        <div>
                          <h5 className="font-medium text-sm mb-2">Stack Trace:</h5>
                          <ScrollArea className="h-48 w-full border rounded-lg">
                            <div className="p-3 font-mono text-xs space-y-1">
                              {thread.stackTrace.map((frame: any, frameIndex: number) => (
                                <div key={frameIndex} className="text-muted-foreground">
                                  at {frame.className}.{frame.methodName}
                                  {frame.fileName && `(${frame.fileName}`}
                                  {frame.lineNumber && frame.lineNumber > 0 && `:${frame.lineNumber}`}
                                  {frame.fileName && ')'}
                                  {frame.nativeMethod && ' [native method]'}
                                </div>
                              ))}
                            </div>
                          </ScrollArea>
                        </div>
                      )}

                      {/* Blocked/Waiting Info */}
                      {(thread.blockedTime || thread.waitedTime || thread.lockName) && (
                        <div className="bg-muted/50 rounded-lg p-3 space-y-2">
                          <h5 className="font-medium text-sm">Bekleme/Blok Bilgisi:</h5>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                            {thread.blockedTime && (
                              <div>
                                <span className="font-medium">Blok Süresi:</span>
                                <div className="text-muted-foreground">{thread.blockedTime}ms</div>
                              </div>
                            )}
                            {thread.waitedTime && (
                              <div>
                                <span className="font-medium">Bekleme Süresi:</span>
                                <div className="text-muted-foreground">{thread.waitedTime}ms</div>
                              </div>
                            )}
                            {thread.lockName && (
                              <div>
                                <span className="font-medium">Kilit:</span>
                                <div className="text-muted-foreground font-mono text-xs">{thread.lockName}</div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}
        </div>

        {filteredThreads.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-muted-foreground py-8">
                <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Filtrelere uygun thread bulunamadı</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
