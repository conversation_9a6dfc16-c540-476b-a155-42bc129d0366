'use client';

import { useState, useEffect } from 'react';
import { 
  Activity, 
  Heart, 
  Cpu, 
  Database, 
  HardDrive, 
  Mail, 
  Shield, 
  Clock,
  TrendingUp,
  Server,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { actuatorService } from '@/lib/api';

interface ActuatorOverviewProps {
  readonly healthStatus: any;
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
  readonly autoRefresh?: boolean;
  readonly refreshInterval?: number;
}

export function ActuatorOverview({
  healthStatus,
  isLoading,
  error,
  onRefresh,
  autoRefresh = false,
  refreshInterval = 5
}: ActuatorOverviewProps) {
  const [metrics, setMetrics] = useState<any>(null);
  const [keyMetrics, setKeyMetrics] = useState<any>({});
  const [metricsLoading, setMetricsLoading] = useState(true);

  const loadKeyMetrics = async () => {
    setMetricsLoading(true);
    try {
      // Load key metrics
      const [
        jvmMemoryUsed,
        jvmMemoryMax,
        systemCpuUsage,
        processCpuUsage,
        httpRequests,
        hikariConnections,
        jvmThreadsLive
      ] = await Promise.all([
        actuatorService.getMetric('jvm.memory.used').catch(() => null),
        actuatorService.getMetric('jvm.memory.max').catch(() => null),
        actuatorService.getMetric('system.cpu.usage').catch(() => null),
        actuatorService.getMetric('process.cpu.usage').catch(() => null),
        actuatorService.getMetric('http.server.requests').catch(() => null),
        actuatorService.getMetric('hikaricp.connections.active').catch(() => null),
        actuatorService.getMetric('jvm.threads.live').catch(() => null)
      ]);

      setKeyMetrics({
        jvmMemoryUsed,
        jvmMemoryMax,
        systemCpuUsage,
        processCpuUsage,
        httpRequests,
        hikariConnections,
        jvmThreadsLive
      });
    } catch (error) {
      console.error('Key metrics load error:', error);
      toast.error('Anahtar metrikler yüklenirken hata oluştu');
    } finally {
      setMetricsLoading(false);
    }
  };

  useEffect(() => {
    loadKeyMetrics();
  }, []);

  // Auto-refresh metrics
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const id = setInterval(() => {
        loadKeyMetrics();
      }, refreshInterval * 1000);
      return () => clearInterval(id);
    }
  }, [autoRefresh, refreshInterval]);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'UP':
        return 'text-green-600 bg-green-100';
      case 'DOWN':
        return 'text-red-600 bg-red-100';
      case 'OUT_OF_SERVICE':
        return 'text-yellow-600 bg-yellow-100';
      case 'UNKNOWN':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'UP':
        return 'Çalışıyor';
      case 'DOWN':
        return 'Çalışmıyor';
      case 'OUT_OF_SERVICE':
        return 'Hizmet Dışı';
      case 'UNKNOWN':
        return 'Bilinmiyor';
      default:
        return status || 'Bilinmiyor';
    }
  };

  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-3">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-6 w-16" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-20" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* System Health Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Overall Health */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Heart className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">Sistem Durumu</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {healthStatus ? (
              <Badge className={getStatusColor(healthStatus.status)}>
                {getStatusText(healthStatus.status)}
              </Badge>
            ) : (
              <Badge variant="secondary">Yükleniyor...</Badge>
            )}
          </CardContent>
        </Card>

        {/* Database Health */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">Veritabanı</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {healthStatus?.components?.db ? (
              <div className="space-y-1">
                <Badge className={getStatusColor(healthStatus.components.db.status)}>
                  {getStatusText(healthStatus.components.db.status)}
                </Badge>
                <p className="text-xs text-muted-foreground">
                  {healthStatus.components.db.details?.database}
                </p>
              </div>
            ) : (
              <Badge variant="secondary">Bilgi Yok</Badge>
            )}
          </CardContent>
        </Card>

        {/* Disk Space */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <HardDrive className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">Disk Alanı</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {healthStatus?.components?.diskSpace ? (
              <div className="space-y-1">
                <Badge className={getStatusColor(healthStatus.components.diskSpace.status)}>
                  {getStatusText(healthStatus.components.diskSpace.status)}
                </Badge>
                <p className="text-xs text-muted-foreground">
                  {formatBytes(healthStatus.components.diskSpace.details?.free)} / {formatBytes(healthStatus.components.diskSpace.details?.total)}
                </p>
              </div>
            ) : (
              <Badge variant="secondary">Bilgi Yok</Badge>
            )}
          </CardContent>
        </Card>

        {/* Mail Service */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">Mail Servisi</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {healthStatus?.components?.mail ? (
              <div className="space-y-1">
                <Badge className={getStatusColor(healthStatus.components.mail.status)}>
                  {getStatusText(healthStatus.components.mail.status)}
                </Badge>
                <p className="text-xs text-muted-foreground">
                  {healthStatus.components.mail.details?.location}
                </p>
              </div>
            ) : (
              <Badge variant="secondary">Bilgi Yok</Badge>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* JVM Memory Usage */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Server className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">JVM Bellek</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <Skeleton className="h-8 w-20" />
            ) : keyMetrics.jvmMemoryUsed && keyMetrics.jvmMemoryMax ? (
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {formatBytes(keyMetrics.jvmMemoryUsed.measurements[0]?.value || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  / {formatBytes(keyMetrics.jvmMemoryMax.measurements[0]?.value || 0)}
                </p>
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">Bilgi Yok</div>
            )}
          </CardContent>
        </Card>

        {/* CPU Usage */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Cpu className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">CPU Kullanımı</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <Skeleton className="h-8 w-20" />
            ) : keyMetrics.systemCpuUsage ? (
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {formatPercentage(keyMetrics.systemCpuUsage.measurements[0]?.value || 0)}
                </div>
                <p className="text-xs text-muted-foreground">Sistem</p>
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">Bilgi Yok</div>
            )}
          </CardContent>
        </Card>

        {/* Active Threads */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">Aktif Thread</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <Skeleton className="h-8 w-20" />
            ) : keyMetrics.jvmThreadsLive ? (
              <div className="text-2xl font-bold">
                {Math.round(keyMetrics.jvmThreadsLive.measurements[0]?.value || 0)}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">Bilgi Yok</div>
            )}
          </CardContent>
        </Card>

        {/* Database Connections */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">DB Bağlantıları</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <Skeleton className="h-8 w-20" />
            ) : keyMetrics.hikariConnections ? (
              <div className="text-2xl font-bold">
                {Math.round(keyMetrics.hikariConnections.measurements[0]?.value || 0)}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">Bilgi Yok</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Refresh Button */}
      <div className="flex justify-center">
        <Button onClick={() => { onRefresh(); loadKeyMetrics(); }} disabled={isLoading || metricsLoading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${(isLoading || metricsLoading) ? 'animate-spin' : ''}`} />
          Metrikleri Yenile
        </Button>
      </div>
    </div>
  );
}
