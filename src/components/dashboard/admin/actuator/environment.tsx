'use client';

import { useState, useEffect } from 'react';
import { 
  Server, 
  Settings, 
  Database, 
  Mail, 
  Shield, 
  RefreshCw,
  Search,
  ChevronDown,
  ChevronRight,
  Eye,
  EyeOff,
  Copy,
  Check
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { toast } from 'sonner';
import { actuatorService } from '@/lib/api';

export function ActuatorEnvironment() {
  const [environment, setEnvironment] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedSources, setExpandedSources] = useState<Set<string>>(new Set());
  const [hiddenValues, setHiddenValues] = useState<Set<string>>(new Set());
  const [copiedKeys, setCopiedKeys] = useState<Set<string>>(new Set());

  const loadEnvironment = async () => {
    setLoading(true);
    try {
      const envData = await actuatorService.getEnvironment();
      setEnvironment(envData);
    } catch (error) {
      console.error('Environment load error:', error);
      toast.error('Ortam bilgileri yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadEnvironment();
  }, []);

  const toggleSource = (sourceName: string) => {
    const newExpanded = new Set(expandedSources);
    if (newExpanded.has(sourceName)) {
      newExpanded.delete(sourceName);
    } else {
      newExpanded.add(sourceName);
    }
    setExpandedSources(newExpanded);
  };

  const toggleValueVisibility = (key: string) => {
    const newHidden = new Set(hiddenValues);
    if (newHidden.has(key)) {
      newHidden.delete(key);
    } else {
      newHidden.add(key);
    }
    setHiddenValues(newHidden);
  };

  const copyToClipboard = async (text: string, key: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedKeys(prev => new Set(prev).add(key));
      setTimeout(() => {
        setCopiedKeys(prev => {
          const newSet = new Set(prev);
          newSet.delete(key);
          return newSet;
        });
      }, 2000);
      toast.success('Panoya kopyalandı');
    } catch (error) {
      toast.error('Kopyalama başarısız');
    }
  };

  const isSensitiveKey = (key: string) => {
    const sensitiveKeys = [
      'password', 'secret', 'key', 'token', 'credential', 'auth',
      'private', 'confidential', 'secure', 'api-key', 'secret-key'
    ];
    return sensitiveKeys.some(sensitive => 
      key.toLowerCase().includes(sensitive.toLowerCase())
    );
  };

  const formatValue = (value: any, key: string) => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground italic">null</span>;
    }
    
    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'true' : 'false'}
        </Badge>
      );
    }
    
    if (typeof value === 'object') {
      return <span className="text-muted-foreground italic">[Object]</span>;
    }
    
    const stringValue = String(value);
    const isHidden = hiddenValues.has(key) || (isSensitiveKey(key) && !hiddenValues.has(key));
    
    return (
      <div className="flex items-center gap-2">
        <span className={`font-mono text-sm ${isHidden ? 'blur-sm select-none' : ''}`}>
          {isHidden && isSensitiveKey(key) ? '••••••••' : stringValue}
        </span>
        {isSensitiveKey(key) && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleValueVisibility(key)}
            className="h-6 w-6 p-0"
          >
            {isHidden ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
          </Button>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => copyToClipboard(stringValue, key)}
          className="h-6 w-6 p-0"
        >
          {copiedKeys.has(key) ? (
            <Check className="h-3 w-3 text-green-600" />
          ) : (
            <Copy className="h-3 w-3" />
          )}
        </Button>
      </div>
    );
  };

  const getSourceIcon = (sourceName: string) => {
    if (sourceName.includes('application')) return <Settings className="h-4 w-4" />;
    if (sourceName.includes('system')) return <Server className="h-4 w-4" />;
    if (sourceName.includes('database') || sourceName.includes('datasource')) return <Database className="h-4 w-4" />;
    if (sourceName.includes('mail')) return <Mail className="h-4 w-4" />;
    if (sourceName.includes('security') || sourceName.includes('ssl')) return <Shield className="h-4 w-4" />;
    return <Settings className="h-4 w-4" />;
  };

  const getSourceDisplayName = (sourceName: string) => {
    if (sourceName.includes('application')) return 'Uygulama Yapılandırması';
    if (sourceName.includes('system')) return 'Sistem Özellikleri';
    if (sourceName.includes('environment')) return 'Ortam Değişkenleri';
    if (sourceName.includes('random')) return 'Rastgele Değerler';
    if (sourceName.includes('server')) return 'Sunucu Yapılandırması';
    return sourceName;
  };

  const filterProperties = (properties: any) => {
    if (!searchTerm) return properties;
    
    const filtered: any = {};
    Object.entries(properties).forEach(([key, value]) => {
      if (key.toLowerCase().includes(searchTerm.toLowerCase()) ||
          String(value).toLowerCase().includes(searchTerm.toLowerCase())) {
        filtered[key] = value;
      }
    });
    return filtered;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-5 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-24 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Search */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h3 className="text-lg font-semibold">Ortam Yapılandırması</h3>
          <p className="text-sm text-muted-foreground">
            Uygulama ve sistem ortam değişkenleri
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Özellik ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <Button onClick={loadEnvironment} disabled={loading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
        </div>
      </div>

      {/* Active Profiles */}
      {environment?.activeProfiles && environment.activeProfiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Aktif Profiller</CardTitle>
            <CardDescription>
              Şu anda etkin olan Spring profilleri
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {environment.activeProfiles.map((profile: string) => (
                <Badge key={profile} variant="default">
                  {profile}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Property Sources */}
      {environment?.propertySources && (
        <div className="space-y-4">
          <h4 className="text-md font-medium">Özellik Kaynakları</h4>
          
          {environment.propertySources.map((source: any, index: number) => {
            const filteredProperties = filterProperties(source.properties || {});
            const hasMatchingProperties = Object.keys(filteredProperties).length > 0;
            
            if (searchTerm && !hasMatchingProperties) return null;
            
            return (
              <Card key={index}>
                <Collapsible 
                  open={expandedSources.has(source.name)}
                  onOpenChange={() => toggleSource(source.name)}
                >
                  <CollapsibleTrigger asChild>
                    <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {getSourceIcon(source.name)}
                          <div>
                            <CardTitle className="text-base">
                              {getSourceDisplayName(source.name)}
                            </CardTitle>
                            <CardDescription>
                              {Object.keys(source.properties || {}).length} özellik
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            {source.name}
                          </Badge>
                          {expandedSources.has(source.name) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </div>
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      {Object.keys(filteredProperties).length > 0 ? (
                        <div className="space-y-2">
                          {Object.entries(filteredProperties).map(([key, propertyInfo]: [string, any]) => (
                            <div key={key} className="border rounded-lg p-3 bg-muted/30">
                              <div className="flex items-start justify-between gap-4">
                                <div className="flex-1 min-w-0">
                                  <div className="font-medium text-sm mb-1 break-all">
                                    {key}
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {formatValue(propertyInfo.value, key)}
                                  </div>
                                  {propertyInfo.origin && (
                                    <div className="text-xs text-muted-foreground mt-1">
                                      Kaynak: {propertyInfo.origin}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center text-muted-foreground py-8">
                          Bu kaynakta özellik bulunamadı
                        </div>
                      )}
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>
      )}

      {/* No Results */}
      {searchTerm && environment?.propertySources && 
       !environment.propertySources.some((source: any) => 
         Object.keys(filterProperties(source.properties || {})).length > 0
       ) && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground py-8">
              <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>"{searchTerm}" için sonuç bulunamadı</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
