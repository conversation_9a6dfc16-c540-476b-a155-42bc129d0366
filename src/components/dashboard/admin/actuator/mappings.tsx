'use client';

import { useState, useEffect } from 'react';
import { 
  Database, 
  Globe, 
  Route, 
  RefreshCw,
  Search,
  Filter,
  ChevronDown,
  ChevronRight,
  Copy,
  Check,
  ExternalLink
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { toast } from 'sonner';
import { actuatorService } from '@/lib/api';

export function ActuatorMappings() {
  const [mappings, setMappings] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [methodFilter, setMethodFilter] = useState('all');
  const [expandedMappings, setExpandedMappings] = useState<Set<string>>(new Set());
  const [copiedPaths, setCopiedPaths] = useState<Set<string>>(new Set());

  const loadMappings = async () => {
    setLoading(true);
    try {
      const mappingsData = await actuatorService.getMappings();
      setMappings(mappingsData);
    } catch (error) {
      console.error('Mappings load error:', error);
      toast.error('URL mapping bilgileri yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMappings();
  }, []);

  const toggleMapping = (mappingId: string) => {
    const newExpanded = new Set(expandedMappings);
    if (newExpanded.has(mappingId)) {
      newExpanded.delete(mappingId);
    } else {
      newExpanded.add(mappingId);
    }
    setExpandedMappings(newExpanded);
  };

  const copyToClipboard = async (text: string, path: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedPaths(prev => new Set(prev).add(path));
      setTimeout(() => {
        setCopiedPaths(prev => {
          const newSet = new Set(prev);
          newSet.delete(path);
          return newSet;
        });
      }, 2000);
      toast.success('Panoya kopyalandı');
    } catch (error) {
      toast.error('Kopyalama başarısız');
    }
  };

  const getMethodColor = (method: string) => {
    switch (method?.toUpperCase()) {
      case 'GET':
        return 'text-green-600 bg-green-100 border-green-200';
      case 'POST':
        return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'PUT':
        return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'DELETE':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'PATCH':
        return 'text-purple-600 bg-purple-100 border-purple-200';
      case 'OPTIONS':
        return 'text-gray-600 bg-gray-100 border-gray-200';
      case 'HEAD':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const extractRequestMappings = () => {
    if (!mappings?.contexts) return [];
    
    const allMappings: any[] = [];
    
    Object.values(mappings.contexts).forEach((context: any) => {
      if (context.mappings?.dispatcherServlets) {
        Object.values(context.mappings.dispatcherServlets).forEach((servlet: any) => {
          Object.entries(servlet).forEach(([key, mapping]: [string, any]) => {
            if (mapping.details) {
              allMappings.push({
                id: key,
                ...mapping.details,
                predicate: mapping.predicate
              });
            }
          });
        });
      }
    });
    
    return allMappings;
  };

  const filteredMappings = extractRequestMappings().filter((mapping: any) => {
    const matchesSearch = !searchTerm || 
      mapping.predicate?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mapping.handlerMethod?.className?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mapping.handlerMethod?.name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesMethod = methodFilter === 'all' || 
      mapping.requestMappingConditions?.methods?.includes(methodFilter.toUpperCase());
    
    return matchesSearch && matchesMethod;
  });

  const getUniqueHttpMethods = () => {
    const methods = new Set<string>();
    extractRequestMappings().forEach((mapping: any) => {
      mapping.requestMappingConditions?.methods?.forEach((method: string) => {
        methods.add(method);
      });
    });
    return Array.from(methods).sort();
  };

  const getMappingStatistics = () => {
    const allMappings = extractRequestMappings();
    const stats = {
      total: allMappings.length,
      byMethod: {} as Record<string, number>
    };
    
    allMappings.forEach((mapping: any) => {
      mapping.requestMappingConditions?.methods?.forEach((method: string) => {
        stats.byMethod[method] = (stats.byMethod[method] || 0) + 1;
      });
    });
    
    return stats;
  };

  const statistics = getMappingStatistics();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-3">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-6 w-16" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h3 className="text-lg font-semibold">URL Mapping'leri</h3>
          <p className="text-sm text-muted-foreground">
            Toplam {statistics.total} endpoint
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Endpoint ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-48"
            />
          </div>
          <Select value={methodFilter} onValueChange={setMethodFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tümü</SelectItem>
              {getUniqueHttpMethods().map((method) => (
                <SelectItem key={method} value={method.toLowerCase()}>
                  {method}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={loadMappings} disabled={loading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
        </div>
      </div>

      {/* HTTP Method Statistics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Route className="h-4 w-4 text-primary" />
              <CardTitle className="text-sm font-medium">Toplam</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statistics.total}
            </div>
          </CardContent>
        </Card>

        {Object.entries(statistics.byMethod).map(([method, count]) => (
          <Card key={method}>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                <CardTitle className="text-sm font-medium">{method}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {count}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Mappings List */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-md font-medium">Endpoint Listesi</h4>
          <Badge variant="secondary">
            {filteredMappings.length} / {statistics.total} endpoint
          </Badge>
        </div>

        <div className="space-y-2">
          {filteredMappings.map((mapping: any, index: number) => (
            <Card key={mapping.id || index}>
              <Collapsible 
                open={expandedMappings.has(mapping.id || index.toString())}
                onOpenChange={() => toggleMapping(mapping.id || index.toString())}
              >
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors py-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <Route className="h-4 w-4 text-primary flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            {mapping.requestMappingConditions?.methods?.map((method: string) => (
                              <Badge key={method} variant="outline" className={`text-xs ${getMethodColor(method)}`}>
                                {method}
                              </Badge>
                            ))}
                          </div>
                          <CardTitle className="text-sm font-medium truncate">
                            {mapping.predicate || 'Bilinmeyen endpoint'}
                          </CardTitle>
                          <CardDescription className="text-xs truncate">
                            {mapping.handlerMethod?.className}.{mapping.handlerMethod?.name}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            copyToClipboard(mapping.predicate || '', mapping.id || index.toString());
                          }}
                          className="h-6 w-6 p-0"
                        >
                          {copiedPaths.has(mapping.id || index.toString()) ? (
                            <Check className="h-3 w-3 text-green-600" />
                          ) : (
                            <Copy className="h-3 w-3" />
                          )}
                        </Button>
                        {expandedMappings.has(mapping.id || index.toString()) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>
                
                <CollapsibleContent>
                  <CardContent className="pt-0">
                    <div className="space-y-4">
                      {/* Handler Method Details */}
                      {mapping.handlerMethod && (
                        <div className="bg-muted/50 rounded-lg p-3">
                          <h5 className="font-medium text-sm mb-2">Handler Method:</h5>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span className="font-medium">Sınıf:</span>
                              <span className="text-muted-foreground font-mono text-xs">
                                {mapping.handlerMethod.className}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="font-medium">Method:</span>
                              <span className="text-muted-foreground font-mono text-xs">
                                {mapping.handlerMethod.name}
                              </span>
                            </div>
                            {mapping.handlerMethod.descriptor && (
                              <div className="flex justify-between">
                                <span className="font-medium">Descriptor:</span>
                                <span className="text-muted-foreground font-mono text-xs">
                                  {mapping.handlerMethod.descriptor}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Request Mapping Conditions */}
                      {mapping.requestMappingConditions && (
                        <div className="bg-muted/50 rounded-lg p-3">
                          <h5 className="font-medium text-sm mb-2">Request Mapping Koşulları:</h5>
                          <div className="space-y-2 text-sm">
                            {mapping.requestMappingConditions.methods && (
                              <div>
                                <span className="font-medium">HTTP Methods:</span>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {mapping.requestMappingConditions.methods.map((method: string) => (
                                    <Badge key={method} variant="outline" className={`text-xs ${getMethodColor(method)}`}>
                                      {method}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {mapping.requestMappingConditions.patterns && (
                              <div>
                                <span className="font-medium">Patterns:</span>
                                <div className="mt-1 space-y-1">
                                  {mapping.requestMappingConditions.patterns.map((pattern: string, idx: number) => (
                                    <div key={idx} className="font-mono text-xs text-muted-foreground bg-background rounded px-2 py-1">
                                      {pattern}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {mapping.requestMappingConditions.consumes && (
                              <div>
                                <span className="font-medium">Consumes:</span>
                                <div className="mt-1 space-y-1">
                                  {mapping.requestMappingConditions.consumes.map((consume: any, idx: number) => (
                                    <div key={idx} className="font-mono text-xs text-muted-foreground">
                                      {consume.mediaType || consume}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {mapping.requestMappingConditions.produces && (
                              <div>
                                <span className="font-medium">Produces:</span>
                                <div className="mt-1 space-y-1">
                                  {mapping.requestMappingConditions.produces.map((produce: any, idx: number) => (
                                    <div key={idx} className="font-mono text-xs text-muted-foreground">
                                      {produce.mediaType || produce}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {mapping.requestMappingConditions.params && mapping.requestMappingConditions.params.length > 0 && (
                              <div>
                                <span className="font-medium">Parameters:</span>
                                <div className="mt-1 space-y-1">
                                  {mapping.requestMappingConditions.params.map((param: string, idx: number) => (
                                    <div key={idx} className="font-mono text-xs text-muted-foreground">
                                      {param}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {mapping.requestMappingConditions.headers && mapping.requestMappingConditions.headers.length > 0 && (
                              <div>
                                <span className="font-medium">Headers:</span>
                                <div className="mt-1 space-y-1">
                                  {mapping.requestMappingConditions.headers.map((header: string, idx: number) => (
                                    <div key={idx} className="font-mono text-xs text-muted-foreground">
                                      {header}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}
        </div>

        {filteredMappings.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-muted-foreground py-8">
                <Route className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Filtrelere uygun endpoint bulunamadı</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
