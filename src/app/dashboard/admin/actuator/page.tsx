'use client';

import { useState, useEffect } from 'react';
import { Activity, RefreshCw, Heart, Cpu, Database, Shield, Server, Clock, BarChart3 } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BackButton } from '@/components/ui/back-button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { actuatorService } from '@/lib/api';
import { ActuatorOverview } from '@/components/dashboard/admin/actuator/overview';
import { ActuatorHealth } from '@/components/dashboard/admin/actuator/health';
import { ActuatorMetrics } from '@/components/dashboard/admin/actuator/metrics';
import { ActuatorEnvironment } from '@/components/dashboard/admin/actuator/environment';
import { ActuatorThreads } from '@/components/dashboard/admin/actuator/threads';
import { ActuatorMappings } from '@/components/dashboard/admin/actuator/mappings';

export default function ActuatorPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const loadHealthStatus = async () => {
    try {
      setError(null);
      const health = await actuatorService.getHealth();
      setHealthStatus(health);
    } catch (error) {
      console.error('Health status load error:', error);
      setError(error instanceof Error ? error.message : 'Bilinmeyen bir hata oluştu');
      toast.error('Sistem durumu yüklenirken hata oluştu');
    }
  };

  const loadData = async () => {
    setLoading(true);
    try {
      await loadHealthStatus();
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Data load error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleRefresh = () => {
    loadData();
    toast.success('Veriler yenilendi');
  };

  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'UP':
        return 'bg-green-500';
      case 'DOWN':
        return 'bg-red-500';
      case 'OUT_OF_SERVICE':
        return 'bg-yellow-500';
      case 'UNKNOWN':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'UP':
        return 'Çalışıyor';
      case 'DOWN':
        return 'Çalışmıyor';
      case 'OUT_OF_SERVICE':
        return 'Hizmet Dışı';
      case 'UNKNOWN':
        return 'Bilinmiyor';
      default:
        return status || 'Bilinmiyor';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex items-center gap-3">
            <BackButton />
            <Activity className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Sistem İzleme</h1>
              <p className="text-muted-foreground">
                Spring Actuator ile sistem durumu ve performans metrikleri
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            {healthStatus && (
              <div className="flex items-center gap-2">
                <div className={`h-3 w-3 rounded-full ${getStatusColor(healthStatus.status)}`} />
                <span className="text-sm font-medium">
                  {getStatusText(healthStatus.status)}
                </span>
              </div>
            )}
            <Button onClick={handleRefresh} disabled={loading} size="sm">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
          </div>
        </div>

        {/* Last Refresh Info */}
        <Card className="border-dashed">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Son güncelleme: {lastRefresh.toLocaleString('tr-TR')}</span>
              <span>Otomatik yenileme: Kapalı</span>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-red-600">
                <Shield className="h-4 w-4" />
                <span className="text-sm font-medium">Hata: {error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span className="hidden sm:inline">Genel Bakış</span>
            </TabsTrigger>
            <TabsTrigger value="health" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              <span className="hidden sm:inline">Sağlık</span>
            </TabsTrigger>
            <TabsTrigger value="metrics" className="flex items-center gap-2">
              <Cpu className="h-4 w-4" />
              <span className="hidden sm:inline">Metrikler</span>
            </TabsTrigger>
            <TabsTrigger value="environment" className="flex items-center gap-2">
              <Server className="h-4 w-4" />
              <span className="hidden sm:inline">Ortam</span>
            </TabsTrigger>
            <TabsTrigger value="threads" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span className="hidden sm:inline">Thread'ler</span>
            </TabsTrigger>
            <TabsTrigger value="mappings" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              <span className="hidden sm:inline">Mapping'ler</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <ActuatorOverview 
              healthStatus={healthStatus}
              isLoading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </TabsContent>

          <TabsContent value="health" className="space-y-6">
            <ActuatorHealth 
              healthStatus={healthStatus}
              isLoading={loading}
              error={error}
              onRefresh={loadHealthStatus}
            />
          </TabsContent>

          <TabsContent value="metrics" className="space-y-6">
            <ActuatorMetrics />
          </TabsContent>

          <TabsContent value="environment" className="space-y-6">
            <ActuatorEnvironment />
          </TabsContent>

          <TabsContent value="threads" className="space-y-6">
            <ActuatorThreads />
          </TabsContent>

          <TabsContent value="mappings" className="space-y-6">
            <ActuatorMappings />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
