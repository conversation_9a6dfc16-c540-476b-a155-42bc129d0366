'use client';

import axios from 'axios';
import { removeAuthToken, getAuthToken } from './auth';

const API_URL = process.env.NEXT_PUBLIC_API_URL ?? 'https://api4j.com.tr';
const LOGIN_ENDPOINT = /*process.env.NEXT_PUBLIC_API_LOGIN_ENDPOINT ??*/ '/auth/user/login';
const SIGNUP_ENDPOINT = '/auth/user/create';
const VERIFY_EMAIL_ENDPOINT = '/auth/user/verify-email';
const FORGOT_PASSWORD_SEND_OTP_ENDPOINT = '/auth/user/forgot-password/send-otp';
const FORGOT_PASSWORD_VALIDATE_OTP_ENDPOINT = '/auth/user/forgot-password/validate-otp';
const FORGOT_PASSWORD_UPDATE_PASSWORD_ENDPOINT = '/auth/user/forgot-password/update-password';

// Define types for API requests and responses
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  responseMessage: string;
  timestamp: string;
  id: number;
  name: string;
  surname: string;
  isNewUser: boolean;
  isDeleted: boolean;
  jwt: string;
}

export interface SignupRequest {
  password: string;
  email: string;
  name: string;
  surname: string;
  personInfoText?: string;
  identityNumber: string;
  birthDate: string;
  mobilePhone: string;
}

export interface SignupResponse {
  responseMessage: string;
  timestamp: number;
  id: number | null;
  name: string | null;
  surname: string | null;
  personInfoText: string | null;
  identityNumber: number | null;
  birthDate: string | null;
  email: string;
  mobilePhone: string | null;
  isNewUser: boolean | null;
  isDeleted: boolean | null;
}

export interface SignupError {
  passwordStrength: string | null;
  password: string | null;
  error: string | null;
}

export interface VerifyEmailRequest {
  otp: string;
  email: string;
}

export interface VerifyEmailResponse {
  responseMessage: string;
  timestamp: number;
  verified: boolean;
  personId: number;
}

export interface VerifyEmailError {
  error: string | null;
  otp: string | null;
}

export interface ForgotPasswordSendOtpRequest {
  email: string;
}

export interface ForgotPasswordSendOtpResponse {
  responseMessage: string;
  timestamp: number;
}

export interface ForgotPasswordValidateOtpRequest {
  otpValue: string;
  otpEmail: string;
}

export interface ForgotPasswordValidateOtpResponse {
  responseMessage: string;
  timestamp: number;
  validated: boolean;
}

export interface ForgotPasswordUpdateRequest {
  password: string;
  validatedOtp: {
    otpValue: string;
    otpEmail: string;
  };
}

export interface ForgotPasswordUpdateResponse {
  responseMessage: string;
  timestamp: number;
  updated: boolean;
}

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token if available
api.interceptors.request.use((config) => {
  const token = getAuthToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Authentication service
export const authService = {
  // Login user
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    try {
      console.log('Sending login request to:', API_URL + LOGIN_ENDPOINT);
      console.log('Login data:', data);

      const response = await api.post<LoginResponse>(LOGIN_ENDPOINT, data);
      console.log('Login response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Login error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                            error.message ??
                            'Login failed. Please check your credentials.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Sign up user
  signup: async (data: SignupRequest): Promise<SignupResponse> => {
    try {
      console.log('Sending signup request to:', API_URL + SIGNUP_ENDPOINT);

      const response = await api.post<SignupResponse>(SIGNUP_ENDPOINT, data);
      console.log('Signup response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Signup error:', error);
      if (axios.isAxiosError(error)) {
        const errorData = error.response?.data as SignupError;

        if (errorData?.passwordStrength) {
          throw new Error(errorData.passwordStrength);
        } else if (errorData?.password) {
          throw new Error(errorData.password);
        } else if (errorData?.error) {
          throw new Error(errorData.error);
        }

        throw new Error(error.response?.data?.responseMessage ??
                        error.message ??
                        'Signup failed. Please try again.');
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Verify email with OTP
  verifyEmail: async (data: VerifyEmailRequest): Promise<VerifyEmailResponse> => {
    try {
      console.log('Sending email verification request to:', API_URL + VERIFY_EMAIL_ENDPOINT);

      const response = await api.post<VerifyEmailResponse>(VERIFY_EMAIL_ENDPOINT, data);
      console.log('Email verification response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Email verification error:', error);
      if (axios.isAxiosError(error)) {
        const errorData = error.response?.data as VerifyEmailError;

        if (errorData?.otp) {
          throw new Error(errorData.otp);
        } else if (errorData?.error) {
          throw new Error(errorData.error);
        }

        throw new Error(error.response?.data?.responseMessage ??
                        error.message ??
                        'Email verification failed. Please try again.');
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Send OTP for forgot password
  forgotPasswordSendOtp: async (data: ForgotPasswordSendOtpRequest): Promise<ForgotPasswordSendOtpResponse> => {
    try {
      console.log('Sending forgot password OTP request to:', API_URL + FORGOT_PASSWORD_SEND_OTP_ENDPOINT);

      const response = await api.post<ForgotPasswordSendOtpResponse>(FORGOT_PASSWORD_SEND_OTP_ENDPOINT, data);
      console.log('Forgot password OTP response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Forgot password OTP error:', error);
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.responseMessage ??
                        error.message ??
                        'Failed to send OTP. Please try again.');
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Validate OTP for forgot password
  forgotPasswordValidateOtp: async (data: ForgotPasswordValidateOtpRequest): Promise<ForgotPasswordValidateOtpResponse> => {
    try {
      console.log('Sending validate OTP request to:', API_URL + FORGOT_PASSWORD_VALIDATE_OTP_ENDPOINT);

      const response = await api.post<ForgotPasswordValidateOtpResponse>(FORGOT_PASSWORD_VALIDATE_OTP_ENDPOINT, data);
      console.log('Validate OTP response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Validate OTP error:', error);
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.responseMessage ??
                        error.message ??
                        'OTP validation failed. Please try again.');
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Update password with validated OTP
  forgotPasswordUpdatePassword: async (data: ForgotPasswordUpdateRequest): Promise<ForgotPasswordUpdateResponse> => {
    try {
      console.log('Sending update password request to:', API_URL + FORGOT_PASSWORD_UPDATE_PASSWORD_ENDPOINT);

      const response = await api.post<ForgotPasswordUpdateResponse>(FORGOT_PASSWORD_UPDATE_PASSWORD_ENDPOINT, data);
      console.log('Update password response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Update password error:', error);
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.responseMessage ??
                        error.message ??
                        'Password update failed. Please try again.');
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    // Use the imported getAuthToken function to check both localStorage and cookies
    return !!getAuthToken();
  },

  // Logout user
  logout: (): void => {
    // Use the imported removeAuthToken function to clear both localStorage and cookies
    removeAuthToken();
  },
};

// Severance pay service
import { SeverancePayFormValues, SeverancePayResponse } from './schemas/severance-pay';

export const severancePayService = {
  // Calculate severance pay
  calculate: async (data: SeverancePayFormValues): Promise<SeverancePayResponse> => {
    try {
      const response = await api.post<SeverancePayResponse>('/api/user/compensation/calculate', data);
      return response.data;
    } catch (error) {
      console.error('Severance pay calculation error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Calculation failed. Please check your input.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },
};

// Trial data types
export interface TrialParty {
  isim: string;
  soyad?: string;
  sifat: string;
}

export interface Trial {
  kayitId: number;
  dosyaId: string;
  dosyaNo: string;
  dosyaTurKod: number;
  dosyaTurKodAciklama: string;
  birimId: string;
  birimOrgKodu: string;
  birimTuru1: string;
  birimTuru2: string;
  birimTuru3: string;
  yerelBirimAd: string;
  tarihSaat: string;
  islemTuru: number;
  islemSonucu: number;
  hakimHeyet: number;
  islemTuruAciklama: string;
  islemSonucuAciklama: string;
  dosyaTaraflari: TrialParty[];
  izinliHakimList: any[];
  talepDurumu: string;
  katilButonAktifMi: boolean;
  token: string;
  isEDurusmaBirimTalepValid: boolean;
  isEDurusmaSaatTalepValid: boolean;
  isEDurusmaGuncellenecek: boolean;
}

// Trials service
export const trialsService = {
  // Get trials
  getTrials: async (): Promise<Trial[]> => {
    try {
      const response = await api.get<Trial[]>('/api/user/trials');
      return response.data;
    } catch (error) {
      console.error('Get trials error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Failed to fetch trials. Please try again.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },
};

// User photo response type
export interface UserPhotoResponse {
  responseMessage: string | null;
  timestamp: number;
  photo: string;
}

// User photo service
export const userPhotoService = {
  // Get user profile photo
  getUserPhoto: async (): Promise<UserPhotoResponse> => {
    try {
      const response = await api.get<UserPhotoResponse>('/api/user/photo');
      return response.data;
    } catch (error) {
      console.error('Get user photo error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Failed to fetch user photo. Please try again.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },
};

// Notification types
export interface UyapNotification {
  mesaj: string;
  baslik: string;
  mesajId: number;
  gonderilmeTarihi: string;
  okunduMu: boolean;
  bildirimId: number;
}

export interface AppNotification {
  id: number;
  title: string;
  message: string;
  date: string;
  read: boolean;
}

// Notifications service
export const notificationsService = {
  // Get UYAP notifications
  getUyapNotifications: async (): Promise<UyapNotification[]> => {
    try {
      const response = await api.get<UyapNotification[]>('/api/user/notifications');
      return response.data;
    } catch (error) {
      console.error('Get UYAP notifications error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Failed to fetch UYAP notifications. Please try again.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Mark UYAP notification as read
  markUyapNotificationAsRead: async (notificationId: number): Promise<void> => {
    try {
      await api.post(`/api/user/notifications/${notificationId}/read`);
    } catch (error) {
      console.error('Mark UYAP notification as read error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Failed to mark notification as read. Please try again.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Get app notifications (placeholder for future implementation)
  getAppNotifications: async (): Promise<AppNotification[]> => {
    // This is a placeholder for future implementation
    // Currently returns an empty array
    return [];
  },
};

// Case data types
export interface Case {
  dosyaId: string;
  dosyaNo: string;
  dosyaDurumKod: number;
  dosyaDurum: string;
  dosyaTurKod: number;
  dosyaTur: string;
  dosyaAcilisTarihi: {
    date: {
      year: number;
      month: number;
      day: number;
    };
    time: {
      hour: number;
      minute: number;
      second: number;
      nano: number;
    };
  };
}

// Case party type
export interface CaseParty {
  adi: string;
  rol: string;
  vekil?: string;
  kisiKurum: string;
}

// Case parties response type
export type CasePartiesResponse = Record<string, CaseParty[]>;

// Import TahsilatReddiyatResponse type
import { TahsilatReddiyatResponse } from './schemas/tahsilat-reddiyat';

// Cases service
export const casesService = {
  // Get active cases
  getActiveCases: async (): Promise<[Case[], number]> => {
    try {
      const response = await api.get<[Case[], number]>('/api/user/cases?active=true');
      return response.data;
    } catch (error) {
      console.error('Get active cases error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Failed to fetch active cases. Please try again.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Get inactive cases
  getInactiveCases: async (): Promise<[Case[], number]> => {
    try {
      const response = await api.get<[Case[], number]>('/api/user/cases?active=false');
      return response.data;
    } catch (error) {
      console.error('Get inactive cases error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Failed to fetch inactive cases. Please try again.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Get all case parties
  getCaseParties: async (): Promise<CasePartiesResponse> => {
    try {
      const response = await api.get<CasePartiesResponse>('/api/user/cases/taraflar-all');
      return response.data;
    } catch (error) {
      console.error('Get case parties error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Failed to fetch case parties. Please try again.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Get case parties by case number
  getCasePartiesByCaseNumber: async (caseNumber: string): Promise<CaseParty[]> => {
    try {
      const response = await api.get<CaseParty[]>(`/api/user/cases/taraflar?caseNumber=${encodeURIComponent(caseNumber)}`);
      return response.data;
    } catch (error) {
      console.error('Get case parties by case number error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Failed to fetch case parties. Please try again.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },

  // Get tahsilat-reddiyat (collections and refunds) data for a case
  getTahsilatReddiyat: async (caseNumber: string): Promise<TahsilatReddiyatResponse> => {
    try {
      const response = await api.get<TahsilatReddiyatResponse>(`/api/user/cases/tahsilat-reddiyat?caseNumber=${encodeURIComponent(caseNumber)}`);

      // Log the complete response for debugging
      console.log('Tahsilat-reddiyat response:', response.data);

      // Check if the response is a valid TahsilatReddiyatResponse
      // If the response has an error field but status is 200, handle it as an error
      if (response.data && 'error' in response.data) {
        console.error('API returned error with 200 status:', response.data);
        throw new Error(response.data.error as string);
      }

      // Validate that the response has the expected structure
      if (!response.data ||
          !('tahsilatList' in response.data) ||
          !('reddiyatList' in response.data) ||
          !('harcList' in response.data)) {
        console.error('Invalid response structure:', response.data);
        throw new Error('Geçersiz API yanıtı. Lütfen daha sonra tekrar deneyin.');
      }

      return response.data;
    } catch (error) {
      console.error('Get tahsilat-reddiyat error:', error);
      if (axios.isAxiosError(error)) {
        // Log the complete error details for debugging
        console.error('API error response:', error.response?.data);

        // Check for specific error formats
        if (error.response?.data?.error) {
          // Format: { "error": "Case info not found for user: <EMAIL> and case number: 2025/12801111" }
          throw new Error(error.response.data.error);
        } else if (error.response?.data?.errorCode && error.response?.data?.error) {
          // Format: { "errorCode": "PRTL_GNL_1-1", "error": "Bu dosya üzerinde ilgili işlemi yapma yetkiniz bulunmamaktadır." }
          throw new Error(`${error.response.data.error} (${error.response.data.errorCode})`);
        }

        // Fallback to other possible error message formats
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Tahsilat ve reddiyat bilgilerini getirirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// User Report types
export interface UyapDetails {
  details: {
    tcKimlikNo: string;
    adi: string;
    soyadi: string;
    bilirkisiMi: boolean;
    arabulucuMu: boolean;
    uzlastirmaciMi: boolean;
    eDevletLevel: number;
    isKamuAvukati: boolean;
    isUzmanArabulucu: boolean;
    portalSozlesmeOnayDVO: {
      onay: boolean;
    };
  };
}

export interface FinancialSummary {
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
}

export interface TaskSummary {
  totalTasks: number;
  taskCountsByStatus: {
    OPEN: number;
    IN_PROGRESS: number;
    COMPLETED: number;
    CANCELLED: number;
  };
  taskCountsByPriority: {
    LOW: number;
    MEDIUM: number;
    HIGH: number;
    CRITICAL: number;
  };
  taskCountsByStatusAndPriority: {
    OPEN: {
      LOW: number;
      MEDIUM: number;
      HIGH: number;
      CRITICAL: number;
    };
    IN_PROGRESS: {
      LOW: number;
      MEDIUM: number;
      HIGH: number;
      CRITICAL: number;
    };
    COMPLETED: {
      LOW: number;
      MEDIUM: number;
      HIGH: number;
      CRITICAL: number;
    };
    CANCELLED: {
      LOW: number;
      MEDIUM: number;
      HIGH: number;
      CRITICAL: number;
    };
  };
}

export interface UserReport {
  id: number;
  name: string;
  surname: string;
  fullName: string;
  email: string;
  mobilePhone: string;
  identityNumber: number;
  birthDate: number[];
  isEmailVerified: boolean;
  isMobilePhoneVerified: boolean;
  isNewUser: boolean;
  uyapDetails: UyapDetails;
  financialSummary: FinancialSummary;
  taskSummary: TaskSummary;
  reportGeneratedAt: number[];
}

// User Report service
export const userReportService = {
  // Get user report
  getUserReport: async (): Promise<UserReport> => {
    try {
      const response = await api.get<UserReport>('/api/user/report');
      return response.data;
    } catch (error) {
      console.error('Get user report error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Failed to fetch user report. Please try again.';
        throw new Error(errorMessage);
      }
      throw new Error('An unexpected error occurred. Please try again.');
    }
  },
};

// Reminders service
import { ReminderFormValues, Reminder } from './schemas/reminders';
import { TaskFormValues, Task, TaskNoteFormValues, TaskNote } from './schemas/tasks';
import { PowerOfAttorneyFormValues, PowerOfAttorney } from './schemas/power-of-attorney';
import { CaseNoteFormValues, CaseNote } from './schemas/case-notes';
import { CaseDetailsFormValues, CaseDetails } from './schemas/case-details';

export const remindersService = {
  // Get all reminders
  getReminders: async (): Promise<Reminder[]> => {
    try {
      const response = await api.get<Reminder[]>('/api/reminders');
      return response.data;
    } catch (error) {
      console.error('Get reminders error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Hatırlatmaları getirirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Create a new reminder
  createReminder: async (data: ReminderFormValues): Promise<Reminder> => {
    try {
      const response = await api.post<Reminder>('/api/reminders', data);
      return response.data;
    } catch (error) {
      console.error('Create reminder error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Hatırlatma oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Update a reminder
  updateReminder: async (id: number, data: ReminderFormValues): Promise<Reminder> => {
    try {
      const response = await api.put<Reminder>(`/api/reminders/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Update reminder error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Hatırlatma güncellenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Delete a reminder
  deleteReminder: async (id: number): Promise<void> => {
    try {
      await api.delete(`/api/reminders/${id}`);
    } catch (error) {
      console.error('Delete reminder error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Hatırlatma silinirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// Tasks service
export const tasksService = {
  // Get all tasks
  getTasks: async (): Promise<Task[]> => {
    try {
      const response = await api.get<Task[]>('/api/tasks');
      return response.data;
    } catch (error) {
      console.error('Get tasks error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Görevleri getirirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Create a new task
  createTask: async (data: TaskFormValues): Promise<Task> => {
    try {
      const response = await api.post<Task>('/api/tasks', data);
      return response.data;
    } catch (error) {
      console.error('Create task error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Görev oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Update a task
  updateTask: async (id: number, data: TaskFormValues): Promise<Task> => {
    try {
      const response = await api.put<Task>(`/api/tasks/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Update task error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Görev güncellenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Delete a task
  deleteTask: async (id: number): Promise<void> => {
    try {
      await api.delete(`/api/tasks/${id}`);
    } catch (error) {
      console.error('Delete task error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Görev silinirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Create a task note
  createTaskNote: async (taskId: number, data: TaskNoteFormValues): Promise<TaskNote> => {
    try {
      const response = await api.post(`/api/tasks/${taskId}/notes`, data);
      return response.data;
    } catch (error) {
      console.error('Create task note error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Not oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Update a task note
  updateTaskNote: async (noteId: number, data: TaskNoteFormValues): Promise<TaskNote> => {
    try {
      const response = await api.put(`/api/tasks/notes/${noteId}`, data);
      return response.data;
    } catch (error) {
      console.error('Update task note error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Not güncellenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Delete a task note
  deleteTaskNote: async (noteId: number): Promise<void> => {
    try {
      await api.delete(`/api/tasks/notes/${noteId}`);
    } catch (error) {
      console.error('Delete task note error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Not silinirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// Case Details service
export const caseDetailsService = {
  // Get case details by case number
  getCaseDetailsByCaseNumber: async (caseNumber: string): Promise<CaseDetails | null> => {
    try {
      const response = await api.get<CaseDetails>(`/api/user/case-details/by-case-number?caseNumber=${encodeURIComponent(caseNumber)}`);
      return response.data;
    } catch (error) {
      console.error('Get case details error:', error);
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        // Return null if case details not found
        return null;
      }
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Dosya detaylarını getirirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Create case details
  createCaseDetails: async (data: CaseDetailsFormValues): Promise<CaseDetails> => {
    try {
      console.log('Creating case details with data:', data);
      const response = await api.post<CaseDetails>('/api/user/case-details', data);
      console.log('Create case details response:', response);
      return response.data;
    } catch (error) {
      console.error('Create case details error:', error);
      if (axios.isAxiosError(error)) {
        console.error('API error response:', error.response?.data);
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Dosya detayları oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Update case details
  updateCaseDetails: async (id: number, data: CaseDetailsFormValues): Promise<CaseDetails> => {
    try {
      console.log(`Updating case details with ID ${id} with data:`, data);
      const response = await api.put<CaseDetails>(`/api/user/case-details/${id}`, data);
      console.log('Update case details response:', response);
      return response.data;
    } catch (error) {
      console.error('Update case details error:', error);
      if (axios.isAxiosError(error)) {
        console.error('API error response:', error.response?.data);
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Dosya detayları güncellenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Delete case details
  deleteCaseDetails: async (id: number): Promise<void> => {
    try {
      await api.delete(`/api/user/case-details/${id}`);
    } catch (error) {
      console.error('Delete case details error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Dosya detayları silinirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// Case Notes service
export const caseNotesService = {
  // Get notes for a specific case
  getCaseNotes: async (caseNumber: string): Promise<CaseNote[]> => {
    try {
      const response = await api.get<CaseNote[]>(`/api/user/cases/notes/case?caseNumber=${encodeURIComponent(caseNumber)}`);
      return response.data;
    } catch (error) {
      console.error('Get case notes error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.message ??
                          'Dosya notlarını getirirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Create a new case note
  createCaseNote: async (data: CaseNoteFormValues): Promise<CaseNote> => {
    try {
      const response = await api.post('/api/user/cases/notes', data);
      return response.data;
    } catch (error) {
      console.error('Create case note error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.message ??
                          'Not oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Update a case note
  updateCaseNote: async (noteId: number, data: Partial<CaseNoteFormValues>): Promise<CaseNote> => {
    try {
      const response = await api.put(`/api/user/cases/notes/${noteId}`, data);
      return response.data;
    } catch (error) {
      console.error('Update case note error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.message ??
                          'Not güncellenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Delete a case note
  deleteCaseNote: async (noteId: number): Promise<void> => {
    try {
      await api.delete(`/api/user/cases/notes/${noteId}`);
    } catch (error) {
      console.error('Delete case note error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.message ??
                          'Not silinirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// Power of Attorney service
export const powerOfAttorneyService = {
  // Get all power of attorneys
  getPowerOfAttorneys: async (): Promise<PowerOfAttorney[]> => {
    try {
      const response = await api.get<PowerOfAttorney[]>('/api/user/cases/power-of-attorneys');
      return response.data;
    } catch (error) {
      console.error('Get power of attorneys error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Vekaletnameleri getirirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Create a new power of attorney
  createPowerOfAttorney: async (data: PowerOfAttorneyFormValues): Promise<PowerOfAttorney> => {
    try {
      const response = await api.post<PowerOfAttorney>('/api/user/cases/power-of-attorneys', data);
      return response.data;
    } catch (error) {
      console.error('Create power of attorney error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Vekaletname oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Update a power of attorney
  updatePowerOfAttorney: async (id: number, data: PowerOfAttorneyFormValues): Promise<PowerOfAttorney> => {
    try {
      const response = await api.put<PowerOfAttorney>(`/api/user/cases/power-of-attorneys/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Update power of attorney error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Vekaletname güncellenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Delete a power of attorney
  deletePowerOfAttorney: async (id: number): Promise<void> => {
    try {
      await api.delete(`/api/user/cases/power-of-attorneys/${id}`);
    } catch (error) {
      console.error('Delete power of attorney error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.responseMessage ??
                          error.message ??
                          'Vekaletname silinirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// Product service
import { Product, ProductCreateRequest, ProductUpdateRequest } from './schemas/products';

export const productsService = {
  // Get all products
  getAllProducts: async (): Promise<Product[]> => {
    try {
      const response = await api.get<Product[]>('/api/admin/products');
      return response.data;
    } catch (error) {
      console.error('Get products error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Ürünler yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get product by ID
  getProductById: async (id: number): Promise<Product> => {
    try {
      const response = await api.get<Product>(`/api/admin/products/${id}`);
      return response.data;
    } catch (error) {
      console.error('Get product by ID error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Ürün yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Create new product
  createProduct: async (data: ProductCreateRequest): Promise<Product> => {
    try {
      const response = await api.post<Product>('/api/admin/products', data);
      return response.data;
    } catch (error) {
      console.error('Create product error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Ürün oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Update product
  updateProduct: async (id: number, data: ProductUpdateRequest): Promise<Product> => {
    try {
      const response = await api.put<Product>(`/api/admin/products/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Update product error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Ürün güncellenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Delete product
  deleteProduct: async (id: number): Promise<void> => {
    try {
      await api.delete(`/api/admin/products/${id}`);
    } catch (error) {
      console.error('Delete product error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Ürün silinirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// Coupon service
import { Coupon, CouponCreateRequest, CouponUpdateRequest, PaginatedCouponResponse, CouponSearchParams } from './schemas/coupons';

export const couponsService = {
  // Get all coupons with pagination and filtering
  getAllCoupons: async (params: CouponSearchParams = {}): Promise<PaginatedCouponResponse> => {
    try {
      const searchParams = new URLSearchParams();

      if (params.page !== undefined) searchParams.append('page', params.page.toString());
      if (params.size !== undefined) searchParams.append('size', params.size.toString());
      if (params.sortBy) searchParams.append('sortBy', params.sortBy);
      if (params.sortDir) searchParams.append('sortDir', params.sortDir);
      if (params.active !== undefined) searchParams.append('active', params.active.toString());
      if (params.search) searchParams.append('search', params.search);

      const url = `/api/admin/coupons${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
      const response = await api.get<PaginatedCouponResponse>(url);
      return response.data;
    } catch (error) {
      console.error('Get coupons error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Kuponlar yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get coupon by ID
  getCouponById: async (id: number): Promise<Coupon> => {
    try {
      const response = await api.get<Coupon>(`/api/admin/coupons/${id}`);
      return response.data;
    } catch (error) {
      console.error('Get coupon by ID error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Kupon yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get coupon by code
  getCouponByCode: async (code: string): Promise<Coupon> => {
    try {
      const response = await api.get<Coupon>(`/api/admin/coupons/code/${encodeURIComponent(code)}`);
      return response.data;
    } catch (error) {
      console.error('Get coupon by code error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Kupon yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Create new coupon
  createCoupon: async (data: CouponCreateRequest): Promise<Coupon> => {
    try {
      const response = await api.post<Coupon>('/api/admin/coupons', data);
      return response.data;
    } catch (error) {
      console.error('Create coupon error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Kupon oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Update coupon
  updateCoupon: async (id: number, data: CouponUpdateRequest): Promise<Coupon> => {
    try {
      const response = await api.put<Coupon>(`/api/admin/coupons/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Update coupon error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Kupon güncellenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Delete/deactivate coupon
  deleteCoupon: async (id: number): Promise<void> => {
    try {
      await api.delete(`/api/admin/coupons/${id}`);
    } catch (error) {
      console.error('Delete coupon error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Kupon silinirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Bulk delete coupons
  bulkDeleteCoupons: async (ids: number[]): Promise<{ successful: number[], failed: { id: number, error: string }[] }> => {
    const results = {
      successful: [] as number[],
      failed: [] as { id: number, error: string }[]
    };

    // Process deletions in parallel but with error handling for each
    const deletePromises = ids.map(async (id) => {
      try {
        await api.delete(`/api/admin/coupons/${id}`);
        results.successful.push(id);
      } catch (error) {
        console.error(`Delete coupon ${id} error:`, error);
        let errorMessage = 'Bilinmeyen hata';
        if (axios.isAxiosError(error)) {
          errorMessage = error.response?.data?.error ??
                        error.response?.data?.responseMessage ??
                        error.message ??
                        'Kupon silinirken hata oluştu';
        }
        results.failed.push({ id, error: errorMessage });
      }
    });

    await Promise.all(deletePromises);
    return results;
  },
};

// Payments service
import { Payment } from './schemas/payments';

export const paymentsService = {
  // Get all payments
  getAllPayments: async (): Promise<Payment[]> => {
    try {
      const response = await api.get<Payment[]>('/api/admin/products/payments');
      return response.data;
    } catch (error) {
      console.error('Get payments error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Ödemeler yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// File management service
import {
  FileListItem,
  FileResponse,
  FileStatistics,
  FileUploadFormValues
} from './schemas/files';

// Admin User Reports service
import { AdminUserReport } from './schemas/user-reports';

export const filesService = {
  // Upload a file
  uploadFile: async (file: File, metadata: FileUploadFormValues): Promise<FileResponse> => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      if (metadata.description) {
        formData.append('description', metadata.description);
      }

      if (metadata.tags) {
        formData.append('tags', metadata.tags);
      }

      formData.append('isPublic', metadata.isPublic.toString());

      const response = await api.post<FileResponse>('/api/admin/files/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('File upload error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Dosya yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get all files
  getAllFiles: async (): Promise<FileListItem[]> => {
    try {
      const response = await api.get<FileListItem[]>('/api/admin/files');
      return response.data;
    } catch (error) {
      console.error('Get files error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Dosyalar yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get file details by ID
  getFileById: async (id: number): Promise<FileResponse> => {
    try {
      const response = await api.get<FileResponse>(`/api/admin/files/${id}`);
      return response.data;
    } catch (error) {
      console.error('Get file details error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Dosya detayları yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Download file by ID
  downloadFile: async (id: number): Promise<Blob> => {
    try {
      const response = await api.get(`/api/admin/files/${id}/download`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Download file error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Dosya indirilirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Delete file by ID
  deleteFile: async (id: number): Promise<void> => {
    try {
      await api.delete(`/api/admin/files/${id}`);
    } catch (error) {
      console.error('Delete file error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Dosya silinirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get file statistics
  getFileStatistics: async (): Promise<FileStatistics> => {
    try {
      const response = await api.get<FileStatistics>('/api/admin/files/statistics');
      return response.data;
    } catch (error) {
      console.error('Get file statistics error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Dosya istatistikleri yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Bulk delete files
  bulkDeleteFiles: async (ids: number[]): Promise<void> => {
    try {
      // Since the API doesn't have a bulk delete endpoint, we'll delete files one by one
      const deletePromises = ids.map(id => filesService.deleteFile(id));
      await Promise.all(deletePromises);
    } catch (error) {
      console.error('Bulk delete files error:', error);
      throw new Error('Dosyalar silinirken hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// Transaction Types service
import {
  TransactionType,
  TransactionTypeCreateRequest,
  TransactionTypeUpdateRequest,
  TransactionTypeSearchParams
} from './schemas/transaction-types';

export const transactionTypesService = {
  // Get all transaction types with optional filtering
  getAllTransactionTypes: async (params: TransactionTypeSearchParams = {}): Promise<TransactionType[]> => {
    try {
      const searchParams = new URLSearchParams();

      if (params.category) {
        searchParams.append('category', params.category);
      }

      const url = `/api/admin/office/transaction-types${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
      const response = await api.get<TransactionType[]>(url);
      return response.data;
    } catch (error) {
      console.error('Get transaction types error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'İşlem türleri yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Create transaction type
  createTransactionType: async (data: TransactionTypeCreateRequest): Promise<TransactionType> => {
    try {
      const response = await api.post<TransactionType>('/api/admin/office/transaction-types', data);
      return response.data;
    } catch (error) {
      console.error('Create transaction type error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'İşlem türü oluşturulurken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Update transaction type
  updateTransactionType: async (id: number, data: TransactionTypeUpdateRequest): Promise<TransactionType> => {
    try {
      const response = await api.put<TransactionType>(`/api/admin/office/transaction-types/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Update transaction type error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'İşlem türü güncellenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Delete transaction type
  deleteTransactionType: async (id: number): Promise<void> => {
    try {
      await api.delete(`/api/admin/office/transaction-types/${id}`);
    } catch (error) {
      console.error('Delete transaction type error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'İşlem türü silinirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Bulk delete transaction types
  bulkDeleteTransactionTypes: async (ids: number[]): Promise<{ successful: number[], failed: { id: number, error: string }[] }> => {
    const results = {
      successful: [] as number[],
      failed: [] as { id: number, error: string }[]
    };

    // Process deletions in parallel but with error handling for each
    const deletePromises = ids.map(async (id) => {
      try {
        await api.delete(`/api/admin/office/transaction-types/${id}`);
        results.successful.push(id);
      } catch (error) {
        console.error(`Delete transaction type ${id} error:`, error);
        let errorMessage = 'Bilinmeyen hata';
        if (axios.isAxiosError(error)) {
          errorMessage = error.response?.data?.error ??
                        error.response?.data?.responseMessage ??
                        error.message ??
                        'İşlem türü silinirken hata oluştu';
        }
        results.failed.push({ id, error: errorMessage });
      }
    });

    await Promise.all(deletePromises);
    return results;
  },
};

// Admin User Reports service
export const adminUserReportsService = {
  // Get all user reports
  getAllUserReports: async (): Promise<AdminUserReport[]> => {
    try {
      const response = await api.get<AdminUserReport[]>('/api/admin/users/reports');
      return response.data;
    } catch (error) {
      console.error('Get user reports error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Kullanıcı raporları yüklenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// Admin Audit Logs service
import {
  AuditLogResponse,
  AuditLogStatisticsResponse,
  AuditLogSearchParams
} from './schemas/audit-logs';

export const adminAuditLogsService = {
  // Get recent audit logs
  getRecentAuditLogs: async (): Promise<AuditLogResponse[]> => {
    try {
      const response = await api.get<AuditLogResponse[]>('/api/admin/audit-logs/recent');
      return response.data;
    } catch (error) {
      console.error('Get recent audit logs error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Son denetim kayıtları alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get audit logs by date range
  getAuditLogsByDateRange: async (params: { startDate: string; endDate: string }): Promise<AuditLogResponse[]> => {
    try {
      const searchParams = new URLSearchParams({
        startDate: params.startDate,
        endDate: params.endDate,
      });

      const response = await api.get<AuditLogResponse[]>(`/api/admin/audit-logs/date-range?${searchParams}`);
      return response.data;
    } catch (error) {
      console.error('Get audit logs by date range error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Tarih aralığına göre denetim kayıtları alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get audit logs by user ID
  getAuditLogsByUserId: async (userId: number): Promise<AuditLogResponse[]> => {
    try {
      const response = await api.get<AuditLogResponse[]>(`/api/admin/audit-logs/user/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Get audit logs by user ID error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Kullanıcıya göre denetim kayıtları alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get audit logs by user email and date range
  getAuditLogsByUserAndDateRange: async (params: { userEmail: string; startDate: string; endDate: string }): Promise<AuditLogResponse[]> => {
    try {
      const searchParams = new URLSearchParams({
        startDate: params.startDate,
        endDate: params.endDate,
      });

      const response = await api.get<AuditLogResponse[]>(`/api/admin/audit-logs/user/${params.userEmail}/date-range?${searchParams}`);
      return response.data;
    } catch (error) {
      console.error('Get audit logs by user and date range error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Kullanıcı ve tarih aralığına göre denetim kayıtları alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get audit logs by endpoint
  getAuditLogsByEndpoint: async (endpointUrl: string): Promise<AuditLogResponse[]> => {
    try {
      const searchParams = new URLSearchParams({
        endpointUrl: endpointUrl,
      });

      const response = await api.get<AuditLogResponse[]>(`/api/admin/audit-logs/endpoint?${searchParams}`);
      return response.data;
    } catch (error) {
      console.error('Get audit logs by endpoint error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Endpoint\'e göre denetim kayıtları alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get audit logs by client IP
  getAuditLogsByClientIp: async (clientIp: string): Promise<AuditLogResponse[]> => {
    try {
      const response = await api.get<AuditLogResponse[]>(`/api/admin/audit-logs/client-ip/${clientIp}`);
      return response.data;
    } catch (error) {
      console.error('Get audit logs by client IP error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'IP adresine göre denetim kayıtları alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get failed requests
  getFailedRequests: async (): Promise<AuditLogResponse[]> => {
    try {
      const response = await api.get<AuditLogResponse[]>('/api/admin/audit-logs/failed-requests');
      return response.data;
    } catch (error) {
      console.error('Get failed requests error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Başarısız istekler alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get slow requests
  getSlowRequests: async (thresholdMs: number = 1000): Promise<AuditLogResponse[]> => {
    try {
      const searchParams = new URLSearchParams({
        thresholdMs: thresholdMs.toString(),
      });

      const response = await api.get<AuditLogResponse[]>(`/api/admin/audit-logs/slow-requests?${searchParams}`);
      return response.data;
    } catch (error) {
      console.error('Get slow requests error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Yavaş istekler alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get audit logs with errors
  getAuditLogsWithErrors: async (): Promise<AuditLogResponse[]> => {
    try {
      const response = await api.get<AuditLogResponse[]>('/api/admin/audit-logs/errors');
      return response.data;
    } catch (error) {
      console.error('Get audit logs with errors error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Hatalı denetim kayıtları alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get audit log statistics
  getAuditStatistics: async (params: { startDate: string; endDate: string }): Promise<AuditLogStatisticsResponse> => {
    try {
      const searchParams = new URLSearchParams({
        startDate: params.startDate,
        endDate: params.endDate,
      });

      const response = await api.get<AuditLogStatisticsResponse>(`/api/admin/audit-logs/statistics?${searchParams}`);
      return response.data;
    } catch (error) {
      console.error('Get audit statistics error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Denetim istatistikleri alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Cleanup old audit logs
  cleanupOldAuditLogs: async (cutoffDate: string): Promise<void> => {
    try {
      const searchParams = new URLSearchParams({
        cutoffDate: cutoffDate,
      });

      await api.delete(`/api/admin/audit-logs/cleanup?${searchParams}`);
    } catch (error) {
      console.error('Cleanup old audit logs error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Eski denetim kayıtları temizlenirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Export audit logs to CSV
  exportAuditLogs: async (logs: AuditLogResponse[]): Promise<Blob> => {
    try {
      // Create CSV content
      const headers = [
        'ID',
        'Endpoint',
        'HTTP Method',
        'Status Code',
        'Processing Time (ms)',
        'User Email',
        'Client IP',
        'Request Time',
        'Response Time',
        'Request Body Size',
        'Response Body Size',
        'Error Message'
      ];

      const csvContent = [
        headers.join(','),
        ...logs.map(log => [
          log.id,
          log.endpointUrl,
          log.httpMethod,
          log.responseStatusCode,
          log.processingTimeMs,
          log.userEmail || '',
          log.clientIpAddress,
          new Date(log.requestTimestamp).toLocaleString('tr-TR'),
          new Date(log.responseTimestamp).toLocaleString('tr-TR'),
          log.requestBodySize || 0,
          log.responseBodySize || 0,
          log.errorMessage || ''
        ].map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
      ].join('\n');

      return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    } catch (error) {
      console.error('Export audit logs error:', error);
      throw new Error('Denetim kayıtları dışa aktarılırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// Server logs service
export const serverLogsService = {
  // Download server log file
  downloadServerLog: async (): Promise<Blob> => {
    try {
      const response = await api.get('/api/admin/logs/server/download', {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Download server log error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Sunucu log dosyası indirilirken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get server log content for viewing
  getServerLogContent: async (): Promise<string> => {
    try {
      const response = await api.get('/api/admin/logs/server/download', {
        responseType: 'text',
      });
      return response.data;
    } catch (error) {
      console.error('Get server log content error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error ??
                          error.response?.data?.responseMessage ??
                          error.message ??
                          'Sunucu log içeriği alınırken hata oluştu. Lütfen tekrar deneyin.';
        throw new Error(errorMessage);
      }
      throw new Error('Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

// Actuator service for Spring Boot monitoring
export const actuatorService = {
  // Get actuator endpoints
  getEndpoints: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get actuator endpoints error:', error);
      throw new Error('Actuator uç noktaları alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get health status
  getHealth: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator/health');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get health status error:', error);
      throw new Error('Sistem sağlık durumu alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get metrics list
  getMetrics: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator/metrics');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get metrics error:', error);
      throw new Error('Sistem metrikleri alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get specific metric
  getMetric: async (metricName: string): Promise<any> => {
    try {
      const response = await fetch(`http://localhost:4244/actuator/metrics/${metricName}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Get metric ${metricName} error:`, error);
      throw new Error(`${metricName} metriği alınırken hata oluştu. Lütfen tekrar deneyin.`);
    }
  },

  // Get environment info
  getEnvironment: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator/env');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get environment error:', error);
      throw new Error('Ortam bilgileri alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get application info
  getInfo: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator/info');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get info error:', error);
      throw new Error('Uygulama bilgileri alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get beans
  getBeans: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator/beans');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get beans error:', error);
      throw new Error('Spring Bean bilgileri alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get thread dump
  getThreadDump: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator/threaddump');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get thread dump error:', error);
      throw new Error('Thread dump alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get loggers
  getLoggers: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator/loggers');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get loggers error:', error);
      throw new Error('Logger bilgileri alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get mappings
  getMappings: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator/mappings');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get mappings error:', error);
      throw new Error('URL mapping bilgileri alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get scheduled tasks
  getScheduledTasks: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator/scheduledtasks');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get scheduled tasks error:', error);
      throw new Error('Zamanlanmış görev bilgileri alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },

  // Get caches
  getCaches: async (): Promise<any> => {
    try {
      const response = await fetch('http://localhost:4244/actuator/caches');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Get caches error:', error);
      throw new Error('Cache bilgileri alınırken hata oluştu. Lütfen tekrar deneyin.');
    }
  },
};

export default api;
